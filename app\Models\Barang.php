<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Barang extends Model
{
    protected $table = 'barang';

    protected $fillable = [
        'kategori_id',
        'nama',
        'spesifikasi',
        'satuan',
        'foto_url',
        'min_stok',
        'aktif'
    ];

    public function kategori()
    {
        return $this->belongsTo(KategoriBarang::class, 'kategori_id');
    }

    public function transaksiStokDetail()
    {
        return $this->hasMany(TransaksiStokDetail::class, 'barang_id');
    }
}
