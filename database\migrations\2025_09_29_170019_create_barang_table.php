<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('barang', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('kategori_id');
            $table->string('nama', 200);
            $table->string('spesifikasi', 200)->nullable();
            $table->string('satuan', 20);
            $table->string('foto_url', 255)->nullable();
            $table->integer('min_stok');
            $table->tinyInteger('aktif')->default(1);
            $table->timestamps();

            $table->foreign('kategori_id')->references('id')->on('kategori_barang');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('barang');
    }
};
