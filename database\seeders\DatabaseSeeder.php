<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\KategoriBarang;
use App\Models\Barang;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create admin user
        User::create([
            'nama' => 'admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin'),
            'role' => 'admin',
            'aktif' => 1,
        ]);

        // Create staff user
        User::create([
            'nama' => 'staff',
            'email' => '<EMAIL>',
            'password' => Hash::make('staff'),
            'role' => 'staf',
            'aktif' => 1,
        ]);

        // Create categories
        $kategori1 = KategoriBarang::create([
            'nama' => 'Elektronik',
            'deskripsi' => 'Barang-barang elektronik',
            'aktif' => 1,
        ]);

        $kategori2 = KategoriBarang::create([
            'nama' => 'Alat Tulis',
            'deskripsi' => 'Alat tulis kantor',
            'aktif' => 1,
        ]);

        // Create sample items
        Barang::create([
            'kategori_id' => $kategori1->id,
            'nama' => 'Laptop Dell',
            'spesifikasi' => 'Intel Core i5, 8GB RAM, 256GB SSD',
            'satuan' => 'unit',
            'min_stok' => 5,
            'aktif' => 1,
        ]);

        Barang::create([
            'kategori_id' => $kategori2->id,
            'nama' => 'Pulpen Pilot',
            'spesifikasi' => 'Tinta biru, 0.7mm',
            'satuan' => 'pcs',
            'min_stok' => 50,
            'aktif' => 1,
        ]);
    }
}
