<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement('DROP VIEW IF EXISTS v_stok_barang');

        DB::statement('
            CREATE VIEW v_stok_barang AS
            SELECT
                b.id as barang_id,
                b.nama,
                b.satuan,
                COALESCE(SUM(
                    CASE
                        WHEN ts.tipe = "masuk" THEN tsd.qty
                        WHEN ts.tipe = "keluar" THEN -tsd.qty
                        WHEN ts.tipe = "penyesuaian" THEN tsd.qty
                        ELSE 0
                    END
                ), 0) as qty_stok,
                b.min_stok,
                CASE
                    WHEN COALESCE(SUM(
                        CASE
                            WHEN ts.tipe = "masuk" THEN tsd.qty
                            WHEN ts.tipe = "keluar" THEN -tsd.qty
                            WHEN ts.tipe = "penyesuaian" THEN tsd.qty
                            ELSE 0
                        END
                    ), 0) <= b.min_stok THEN 1
                    ELSE 0
                END as perlu_restock
            FROM barang b
            LEFT JOIN transaksi_stok_detail tsd ON b.id = tsd.barang_id
            LEFT JOIN transaksi_stok ts ON tsd.transaksi_id = ts.id
            WHERE b.aktif = 1
            GROUP BY b.id, b.nama, b.satuan, b.min_stok
        ');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement('DROP VIEW IF EXISTS v_stok_barang');
    }
};
