<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

// Test route untuk melihat user yang ada
Route::get('/test-users', function () {
    $users = \App\Models\User::all();
    return response()->json([
        'users' => $users->map(function($user) {
            return [
                'id' => $user->id,
                'nama' => $user->nama,
                'email' => $user->email,
                'role' => $user->role,
                'aktif' => $user->aktif,
            ];
        })
    ]);
});

// Test route untuk debug login dengan username
Route::post('/test-login', function (\Illuminate\Http\Request $request) {
    $nama = $request->nama;
    $password = $request->password;

    // 1. Cek apakah user ada
    $user = \App\Models\User::where('nama', $nama)->first();
    if (!$user) {
        return response()->json(['error' => 'User not found', 'nama' => $nama]);
    }

    // 2. Cek password
    $passwordCheck = \Hash::check($password, $user->password);
    if (!$passwordCheck) {
        return response()->json(['error' => 'Password incorrect', 'user' => $user->toArray()]);
    }

    // 3. Cek field aktif
    if ($user->aktif != 1) {
        return response()->json(['error' => 'User not active', 'user' => $user->toArray()]);
    }

    // 4. Test Auth::attempt
    $attempt1 = \Auth::attempt(['nama' => $nama, 'password' => $password]);
    $attempt2 = \Auth::attempt(['nama' => $nama, 'password' => $password, 'aktif' => 1]);

    return response()->json([
        'user_found' => true,
        'password_correct' => $passwordCheck,
        'user_active' => $user->aktif == 1,
        'auth_attempt_basic' => $attempt1,
        'auth_attempt_with_aktif' => $attempt2,
        'user' => $user->toArray()
    ]);
});

require __DIR__.'/auth.php';
