<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
            {{ __('Dashboard') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <h3 class="text-lg font-semibold mb-4">Selamat datang di Dedhar Inventory System!</h3>
                    <p class="mb-4">Anda berhasil login sebagai: <strong>{{ Auth::user()->nama }}</strong></p>
                    <p class="mb-4">Role: <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded">{{ Auth::user()->role }}</span></p>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                        <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg">
                            <h4 class="font-semibold text-blue-800 dark:text-blue-200">Total Kategori</h4>
                            <p class="text-2xl font-bold text-blue-600 dark:text-blue-300">{{ \App\Models\KategoriBarang::count() }}</p>
                        </div>
                        <div class="bg-green-50 dark:bg-green-900 p-4 rounded-lg">
                            <h4 class="font-semibold text-green-800 dark:text-green-200">Total Barang</h4>
                            <p class="text-2xl font-bold text-green-600 dark:text-green-300">{{ \App\Models\Barang::count() }}</p>
                        </div>
                        <div class="bg-yellow-50 dark:bg-yellow-900 p-4 rounded-lg">
                            <h4 class="font-semibold text-yellow-800 dark:text-yellow-200">Total User</h4>
                            <p class="text-2xl font-bold text-yellow-600 dark:text-yellow-300">{{ \App\Models\User::count() }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
